import { NextResponse } from 'next/server';
import { getAltSeasonData } from '@/lib/utils';
import { getCacheInfo } from '@/lib/cache';

export async function GET() {
  try {
    const data = await getAltSeasonData();
    const cacheInfo = getCacheInfo();
    
    return NextResponse.json({
      success: true,
      data,
      cache: cacheInfo
    });
  } catch (error) {
    console.error('Errore API altseason:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto',
        data: null
      },
      { status: 500 }
    );
  }
}

// Opzionale: endpoint per forzare il refresh della cache
export async function POST() {
  try {
    // Importiamo clearCache solo quando necessario
    const { clearCache } = await import('@/lib/cache');
    clearCache();
    
    const data = await getAltSeasonData();
    
    return NextResponse.json({
      success: true,
      message: 'Cache aggiornata',
      data
    });
  } catch (error) {
    console.error('Errore refresh cache:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      },
      { status: 500 }
    );
  }
}
