import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "AltSeason Index Tracker",
  description:
    "Monitora in tempo reale l'Altcoin Season Index - la percentuale delle TOP 50 criptovalute che stanno performando meglio di Bitcoin negli ultimi 90 giorni.",
  keywords: [
    "altcoin",
    "bitcoin",
    "crypto",
    "altseason",
    "cryptocurrency",
    "index",
    "tracker",
  ],
  authors: [{ name: "AltSeason Tracker" }],
  manifest: "/manifest.json",
  themeColor: "#1f2937",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "AltSeason Index",
  },
  openGraph: {
    title: "AltSeason Index Tracker",
    description: "Monitora l'Altcoin Season Index in tempo reale",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AltSeason Index Tracker",
    description: "Monitora l'Altcoin Season Index in tempo reale",
  },
  icons: {
    icon: [
      { url: "/icons/icon.svg", type: "image/svg+xml" },
      { url: "/icons/icon-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/icons/icon-512x512.png", sizes: "512x512", type: "image/png" },
    ],
    apple: [
      { url: "/icons/icon-152x152.png", sizes: "152x152", type: "image/png" },
    ],
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: "#1f2937",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it" className={inter.variable}>
      <body className="font-sans antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
        {children}
      </body>
    </html>
  );
}
