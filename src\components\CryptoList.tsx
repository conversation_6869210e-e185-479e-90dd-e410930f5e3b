"use client";

import { formatMarketCap, formatPercentage, formatPrice } from "@/lib/cmc";
import { ProcessedCrypto } from "@/types";
import Image from "next/image";

interface CryptoListProps {
  cryptos: ProcessedCrypto[];
  btcChange90d: number;
}

export default function CryptoList({ cryptos, btcChange90d }: CryptoListProps) {
  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="mb-6 px-2 sm:px-0">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Altcoin Performance Ranking
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Top 50 altcoin ordinate per performance degli ultimi 90 giorni vs
          Bitcoin ({formatPercentage(btcChange90d)})
        </p>
      </div>

      {/* Header mobile */}
      <div className="grid md:hidden grid-cols-[36px_1fr_72px] gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-t-lg text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
        <div className="text-left">Rank</div>
        <div>Nome</div>
        <div className="text-right">90d %</div>
      </div>

      {/* Header per desktop */}
      <div
        className="hidden md:grid gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-t-lg text-sm font-medium text-gray-700 dark:text-gray-300"
        style={{ gridTemplateColumns: "60px 1fr 120px 120px 120px 80px" }}
      >
        <div>Rank</div>
        <div>Nome</div>
        <div>Prezzo</div>
        <div>Market Cap</div>
        <div className="flex items-center space-x-1">
          <span>90d Change</span>
          <span className="text-blue-500">↓</span>
        </div>
        <div>vs BTC</div>
      </div>

      {/* Lista crypto */}
      <div className="bg-white dark:bg-gray-900 rounded-b-lg md:rounded-t-none border border-gray-200 dark:border-gray-700 divide-y divide-gray-200 dark:divide-gray-700">
        {cryptos.map((crypto) => (
          <CryptoRow key={crypto.id} crypto={crypto} />
        ))}
      </div>
    </div>
  );
}

function CryptoRow({ crypto }: { crypto: ProcessedCrypto }) {
  const isOutperforming = crypto.outperformsBTC;
  const isBitcoin = crypto.isBitcoin;

  return (
    <div
      className={`px-2 sm:p-2 transition-all duration-200 hover:shadow-sm ${
        isBitcoin
          ? "bg-orange-50 dark:bg-orange-900/20 border-l-4 border-orange-500 hover:bg-orange-100 dark:hover:bg-orange-900/30"
          : "hover:bg-gray-50 dark:hover:bg-gray-800"
      }`}
    >
      {/* Layout mobile */}
      <div className="md:hidden grid grid-cols-[36px_1fr_72px] gap-2 items-center min-h-[56px]">
        {/* Rank */}
        <div className="text-xs font-medium text-gray-500 dark:text-gray-400 flex items-center justify-start">
          #{crypto.rank}
        </div>
        {/* Logo, nome e simbolo */}
        <div className="flex items-center space-x-2 min-w-0">
          <div className="flex-shrink-0 w-8 h-8 relative flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
            {crypto.logo ? (
              <Image
                src={crypto.logo}
                alt={crypto.name}
                width={32}
                height={32}
                className="rounded-full object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                }}
              />
            ) : (
              <span className="text-xs font-bold text-gray-500 dark:text-gray-400">
                {crypto.symbol.slice(0, 2)}
              </span>
            )}
          </div>
          <div className="truncate">
            <div className="font-medium text-gray-900 dark:text-white truncate">
              {crypto.symbol}
            </div>
            {/* <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {crypto.name}
            </div> */}
          </div>
        </div>
        {/* Percentuale 90d */}
        <div
          className={`text-sm font-medium flex items-center justify-end space-x-1 ${
            crypto.change90d >= 0 ? "text-green-600" : "text-red-600"
          }`}
        >
          <span className="text-xs">{crypto.change90d >= 0 ? "▲" : "▼"}</span>
          <span>{formatPercentage(crypto.change90d)}</span>
        </div>
      </div>

      {/* Layout desktop */}
      <div
        className="hidden md:grid gap-4 items-center"
        style={{ gridTemplateColumns: "60px 1fr 120px 120px 120px 80px" }}
      >
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
            #{crypto.rank}
          </span>
        </div>

        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 relative flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-full">
            {crypto.logo ? (
              <Image
                src={crypto.logo}
                alt={crypto.name}
                width={32}
                height={32}
                className="rounded-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                }}
              />
            ) : (
              <span className="text-xs font-bold text-gray-500 dark:text-gray-400">
                {crypto.symbol.slice(0, 2)}
              </span>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900 dark:text-white">
              {crypto.name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {crypto.symbol}
            </div>
          </div>
        </div>

        <div className="font-medium text-gray-900 dark:text-white">
          {formatPrice(crypto.price)}
        </div>

        <div className="text-gray-600 dark:text-gray-400">
          {formatMarketCap(crypto.marketCap)}
        </div>

        <div
          className={`font-medium flex items-center space-x-1 ${
            crypto.change90d >= 0 ? "text-green-600" : "text-red-600"
          }`}
        >
          <span className="text-xs">{crypto.change90d >= 0 ? "▲" : "▼"}</span>
          <span>{formatPercentage(crypto.change90d)}</span>
        </div>

        <div className="flex items-center justify-center">
          {isBitcoin ? (
            <span className="text-sm font-bold text-orange-600">BTC</span>
          ) : (
            <span
              className={`text-lg font-bold ${
                isOutperforming ? "text-green-600" : "text-red-600"
              }`}
            >
              {isOutperforming ? "↗" : "↘"}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
