export function StatCardLoader() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="text-center flex flex-col items-center justify-center">
        <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
        <div className="h-3 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

export function GaugeLoader() {
  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div className="relative w-64 h-64 mb-6">
        {/* Cerchio di sfondo */}
        <div className="w-full h-full rounded-full border-8 border-gray-200 dark:border-gray-700 animate-pulse"></div>

        {/* Testo centrale */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="h-12 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
          <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Indicatore di stato */}
      <div className="text-center">
        <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

export function TableLoader() {
  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="mb-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2 w-80"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-96"></div>
      </div>

      {/* Header mobile */}
      <div className="grid grid-cols-[36px_1fr_72px] md:hidden gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-t-lg text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
        <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>

      {/* Header per desktop */}
      <div
        className="hidden md:grid gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-t-lg"
        style={{ gridTemplateColumns: "60px 1fr 120px 120px 120px 80px" }}
      >
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
          ></div>
        ))}
      </div>

      {/* Lista crypto skeleton */}
      <div className="bg-white dark:bg-gray-900 rounded-b-lg md:rounded-t-none border border-gray-200 dark:border-gray-700 divide-y divide-gray-200 dark:divide-gray-700">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="p-4">
            {/* Layout mobile */}
            <div className="md:hidden grid grid-cols-[36px_1fr_72px] gap-2 items-center min-h-[56px]">
              <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex items-center space-x-2 min-w-0">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div>
                  <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
                  <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse justify-self-end"></div>
            </div>

            {/* Layout desktop */}
            <div
              className="hidden md:grid gap-4 items-center"
              style={{ gridTemplateColumns: "60px 1fr 120px 120px 120px 80px" }}
            >
              <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div>
                  <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
                  <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function NumberLoader({ className = "" }: { className?: string }) {
  return (
    <div
      className={`bg-gray-200 dark:bg-gray-700 rounded animate-pulse ${className}`}
    ></div>
  );
}
