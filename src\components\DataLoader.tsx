export function StatCardLoader() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="text-center flex flex-col items-center justify-center">
        <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
        <div className="h-3 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

export function GaugeLoader() {
  return (
    <div className="flex flex-col items-center justify-center p-4 sm:p-6 md:p-8">
      <div className="relative w-48 h-48 sm:w-56 sm:h-56 md:w-64 md:h-64 mb-4 sm:mb-6">
        {/* Cerchio SVG di sfondo */}
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 200 200"
        >
          <circle
            cx="100"
            cy="100"
            r="90"
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            className="text-gray-200 dark:text-gray-700"
          />

          {/* Cerchio animato che simula il caricamento */}
          <circle
            cx="100"
            cy="100"
            r="90"
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            strokeDasharray="565.48" // 2 * π * 90
            strokeDashoffset="282.74" // metà circonferenza
            strokeLinecap="round"
            className="text-gray-400 dark:text-gray-600 animate-pulse"
          />
        </svg>

        {/* Testo centrale */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="h-12 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Indicatore di stato */}
      <div className="text-center px-2">
        <div className="h-5 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
        <div className="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    </div>
  );
}

export function TableLoader() {
  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="mb-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2 w-80"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-96"></div>
      </div>

      {/* Header mobile */}
      <div className="grid grid-cols-[36px_1fr_72px] md:hidden gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-t-lg text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide">
        <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>

      {/* Header per desktop */}
      <div
        className="hidden md:grid gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-t-lg"
        style={{ gridTemplateColumns: "60px 1fr 120px 120px 120px 80px" }}
      >
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
          ></div>
        ))}
      </div>

      {/* Lista crypto skeleton */}
      <div className="bg-white dark:bg-gray-900 rounded-b-lg md:rounded-t-none border border-gray-200 dark:border-gray-700 divide-y divide-gray-200 dark:divide-gray-700">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="p-4">
            {/* Layout mobile */}
            <div className="md:hidden grid grid-cols-[36px_1fr_72px] gap-2 items-center min-h-[56px]">
              <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex items-center space-x-2 min-w-0">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div>
                  <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
                  <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse justify-self-end"></div>
            </div>

            {/* Layout desktop */}
            <div
              className="hidden md:grid gap-4 items-center"
              style={{ gridTemplateColumns: "60px 1fr 120px 120px 120px 80px" }}
            >
              <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div>
                  <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
                  <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function NumberLoader({ className = "" }: { className?: string }) {
  return (
    <div
      className={`bg-gray-200 dark:bg-gray-700 rounded animate-pulse ${className}`}
    ></div>
  );
}
