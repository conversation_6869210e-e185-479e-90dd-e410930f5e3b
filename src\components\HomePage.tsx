"use client";

import AltSeasonGauge from "@/components/AltSeasonGauge";
import CryptoList from "@/components/CryptoList";
import {
  GaugeLoader,
  StatCardLoader,
  TableLoader,
} from "@/components/DataLoader";
import ErrorPage from "@/components/ErrorPage";
import StatusIndicator from "@/components/StatusIndicator";
import { formatNumberIT } from "@/lib/cmc";
import { getTimeAgo } from "@/lib/utils";
import { AltSeasonResult } from "@/types";
import { useEffect, useState } from "react";

export default function HomePage() {
  const [data, setData] = useState<AltSeasonResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const response = await fetch("/api/altseason");
        if (!response.ok) {
          throw new Error("Errore nel caricamento dei dati");
        }
        const result = await response.json();
        if (result.success && result.data) {
          setData(result.data);
        } else {
          throw new Error(result.error || "Dati non validi ricevuti dall'API");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Errore sconosciuto");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (error) {
    return <ErrorPage error={error} />;
  }

  // Controllo rigoroso: i dati devono essere COMPLETAMENTE caricati
  if (
    loading ||
    !data ||
    typeof data.index !== "number" ||
    typeof data.outperformingCount !== "number" ||
    typeof data.totalAltcoins !== "number" ||
    typeof data.btcChange90d !== "number" ||
    !data.cryptos ||
    !Array.isArray(data.cryptos) ||
    !data.lastUpdated
  ) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        {/* Header */}
        <header className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8">
            <div className="text-center">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                AltSeason Index
              </h1>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto px-2 sm:px-0">
                Misura quante delle top 50 altcoin stanno performando meglio di
                Bitcoin negli ultimi 90 giorni.
              </p>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8">
          {/* Main Gauge */}
          <section className="mb-12 flex justify-center">
            <GaugeLoader />
          </section>
          {/* Status Indicator */}
          <section className="mb-8">
            <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </section>

          {/* Stats Cards */}
          <section className="mb-12">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
              <StatCardLoader />
              <StatCardLoader />
              <StatCardLoader />
              <StatCardLoader />
            </div>
          </section>

          {/* Crypto List */}
          <section>
            <TableLoader />
          </section>
        </main>

        {/* Footer */}
        <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8">
            <div className="text-center text-sm text-gray-600 dark:text-gray-400">
              <p className="mb-2">
                Dati aggiornati ogni 60 minuti — Fonte: CoinMarketCap
              </p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8">
          <div className="text-center">
            <h1 className=" text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
              AltSeason Index
            </h1>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto px-2 sm:px-0">
              Misura quante delle top 50 altcoin stanno performando meglio di
              Bitcoin negli ultimi 90 giorni.
            </p>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8">
        {/* Main Gauge */}
        <section className="mb-8 flex justify-center">
          <AltSeasonGauge index={data.index} status={data.status} />
        </section>
        {/* Status Indicator */}
        <section className="mb-8">
          <StatusIndicator index={data.index} status={data.status} />
        </section>

        {/* Stats Cards */}
        <section className="mb-12">
          <div className="mb-4 grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
            {/* Altcoin che battono BTC */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <div className="flex flex-col items-center justify-center">
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-3xl font-extrabold text-green-600 dark:text-green-400 leading-none">
                      {data.outperformingCount}
                    </span>
                    <span className="text-2xl text-gray-400 dark:text-gray-600 leading-none">
                      /
                    </span>
                    <span className="text-xl text-gray-900 dark:text-gray-400 font-medium leading-none">
                      {data.totalAltcoins}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Altcoin che battono BTC
                  </div>
                </div>
              </div>
            </div>

            {/* Mediana performance 90 giorni altcoin (escludendo BTC) */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <div className="flex flex-col items-center justify-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 leading-none">
                    {(() => {
                      const altcoins = data.cryptos.filter(
                        (c) => !c.isBitcoin && typeof c.change90d === "number"
                      );
                      if (!altcoins.length) return "-";
                      const sorted = altcoins
                        .map((c) => c.change90d)
                        .sort((a, b) => a - b);
                      const mid = Math.floor(sorted.length / 2);
                      const median =
                        sorted.length % 2 !== 0
                          ? sorted[mid]
                          : (sorted[mid - 1] + sorted[mid]) / 2;
                      return `${median >= 0 ? "+" : ""}${formatNumberIT(
                        median,
                        1
                      )}%`;
                    })()}
                  </div>
                  <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Mediana 90 giorni altcoin
                  </div>
                </div>
              </div>
            </div>
            {/* Bitcoin 90 giorni */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <div
                  className={`text-2xl font-bold ${
                    data.btcChange90d >= 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {data.btcChange90d >= 0 ? "+" : ""}
                  {formatNumberIT(data.btcChange90d, 1)}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Bitcoin 90 giorni
                </div>
              </div>
            </div>
          </div>

          {/* Totale altcoin analizzate */}
          {/* <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {data.totalAltcoins}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Totale altcoin analizzate
                </div>
              </div>
            </div> */}
        </section>

        {/* Crypto List */}
        <section>
          <CryptoList cryptos={data.cryptos} btcChange90d={data.btcChange90d} />
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8">
          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            <p className="mb-2">
              Dati aggiornati ogni 60 minuti — Fonte: CoinMarketCap
            </p>
            <p>Ultimo aggiornamento: {getTimeAgo(data.lastUpdated)}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
