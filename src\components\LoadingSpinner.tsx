export default function LoadingSpinner() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8">
      <div className="relative w-32 h-32 mb-8">
        {/* Spinner animato */}
        <div className="absolute inset-0 border-4 border-gray-200 dark:border-gray-700 rounded-full"></div>
        <div className="absolute inset-0 border-4 border-transparent border-t-blue-500 rounded-full animate-spin"></div>
      </div>
      
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Caricamento AltSeason Index
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Recupero dati da CoinMarketCap...
        </p>
      </div>
    </div>
  );
}

export function LoadingGauge() {
  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div className="relative w-64 h-64 mb-6">
        {/* Cerchio di sfondo */}
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 200 200"
        >
          <circle
            cx="100"
            cy="100"
            r="90"
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            className="text-gray-200 dark:text-gray-700"
          />
          
          {/* Cerchio animato */}
          <circle
            cx="100"
            cy="100"
            r="90"
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            strokeDasharray="565.48"
            strokeDashoffset="282.74"
            strokeLinecap="round"
            className="text-blue-500 animate-pulse"
          />
        </svg>
        
        {/* Testo centrale */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-4xl md:text-5xl font-bold text-gray-400 dark:text-gray-600">
            --.--%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            AltSeason Index
          </div>
        </div>
      </div>
      
      <div className="text-center">
        <div className="text-lg font-semibold text-gray-400 dark:text-gray-600">
          Caricamento...
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Analisi in corso
        </div>
      </div>
    </div>
  );
}

export function LoadingList() {
  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="mb-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 animate-pulse"></div>
      </div>
      
      {/* Header skeleton */}
      <div className="hidden md:grid md:grid-cols-6 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-t-lg">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        ))}
      </div>
      
      {/* Lista skeleton */}
      <div className="bg-white dark:bg-gray-900 rounded-b-lg md:rounded-t-none border border-gray-200 dark:border-gray-700 divide-y divide-gray-200 dark:divide-gray-700">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="p-4">
            <div className="md:hidden">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                  <div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse mb-1"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-12 animate-pulse"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse mb-1"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-12 animate-pulse"></div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12 animate-pulse"></div>
                </div>
              </div>
            </div>
            
            <div className="hidden md:grid md:grid-cols-6 gap-4 items-center">
              {[...Array(6)].map((_, j) => (
                <div key={j} className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
