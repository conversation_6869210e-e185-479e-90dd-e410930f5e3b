import { AltSeasonResult, CacheData, CACHE_DURATION } from '@/types';

// Cache in memoria per Vercel (dato che non abbiamo Redis)
let memoryCache: CacheData | null = null;

export function getCachedData(): AltSeasonResult | null {
  if (!memoryCache) return null;
  
  const now = Date.now();
  
  // Verifica se la cache è scaduta
  if (now > memoryCache.expiresAt) {
    memoryCache = null;
    return null;
  }
  
  return memoryCache.data;
}

export function setCachedData(data: AltSeasonResult): void {
  const now = Date.now();
  
  memoryCache = {
    data,
    timestamp: now,
    expiresAt: now + CACHE_DURATION
  };
}

export function isCacheValid(): boolean {
  if (!memoryCache) return false;
  
  const now = Date.now();
  return now <= memoryCache.expiresAt;
}

export function getCacheInfo(): { 
  hasCache: boolean; 
  isValid: boolean; 
  expiresIn: number; 
  lastUpdated: string | null;
} {
  if (!memoryCache) {
    return {
      hasCache: false,
      isValid: false,
      expiresIn: 0,
      lastUpdated: null
    };
  }
  
  const now = Date.now();
  const isValid = now <= memoryCache.expiresAt;
  const expiresIn = Math.max(0, memoryCache.expiresAt - now);
  
  return {
    hasCache: true,
    isValid,
    expiresIn,
    lastUpdated: new Date(memoryCache.timestamp).toISOString()
  };
}

export function clearCache(): void {
  memoryCache = null;
}
