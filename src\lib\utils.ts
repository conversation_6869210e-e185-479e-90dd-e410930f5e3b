import { AltSeasonResult, ProcessedCrypto } from "@/types";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { getCachedData, setCachedData } from "./cache";
import {
  calculateAltSeasonIndex,
  fetchCryptoData,
  filterValidAltcoins,
  getBitcoinData,
  getMarketStatus,
} from "./cmc";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export async function getAltSeasonData(): Promise<AltSeasonResult> {
  // Controlla prima la cache
  const cachedData = getCachedData();
  if (cachedData) {
    console.log("Dati serviti dalla cache");
    return cachedData;
  }

  console.log("Fetching nuovi dati da CMC...");

  try {
    // Fetch dati da CoinMarketCap
    const allCryptos = await fetchCryptoData();

    // Ottieni i dati di Bitcoin
    const bitcoinData = getBitcoinData(allCryptos);
    if (!bitcoinData) {
      throw new Error("Dati Bitcoin non trovati");
    }

    const btcChange90d = bitcoinData.quote.USD.percent_change_90d;

    // Filtra le altcoin valide
    const validAltcoins = filterValidAltcoins(allCryptos);

    // Prendi solo le prime 50 altcoin (dopo aver escluso BTC e token non validi)
    const top50Altcoins = validAltcoins.slice(0, 50);

    // Calcola l'indice
    const { index, outperformingCount } = calculateAltSeasonIndex(
      top50Altcoins,
      btcChange90d
    );

    // Processa i dati per l'UI (altcoin)
    const processedAltcoins: ProcessedCrypto[] = top50Altcoins.map(
      (crypto) => ({
        id: crypto.id,
        name: crypto.name,
        symbol: crypto.symbol,
        slug: crypto.slug,
        rank: crypto.cmc_rank,
        price: crypto.quote.USD.price,
        marketCap: crypto.quote.USD.market_cap,
        change90d: crypto.quote.USD.percent_change_90d,
        outperformsBTC: crypto.quote.USD.percent_change_90d > btcChange90d,
        logo: `https://s2.coinmarketcap.com/static/img/coins/64x64/${crypto.id}.png`,
        isBitcoin: false,
      })
    );

    // Aggiungi Bitcoin ai dati
    const processedBitcoin: ProcessedCrypto = {
      id: bitcoinData.id,
      name: bitcoinData.name,
      symbol: bitcoinData.symbol,
      slug: bitcoinData.slug,
      rank: bitcoinData.cmc_rank,
      price: bitcoinData.quote.USD.price,
      marketCap: bitcoinData.quote.USD.market_cap,
      change90d: btcChange90d,
      outperformsBTC: false, // Bitcoin non può superare se stesso
      logo: `https://s2.coinmarketcap.com/static/img/coins/64x64/${bitcoinData.id}.png`,
      isBitcoin: true,
    };

    // Combina altcoin e Bitcoin
    const allProcessedCryptos = [...processedAltcoins, processedBitcoin];

    // Ordina per performance 90 giorni (dal migliore al peggiore)
    allProcessedCryptos.sort((a, b) => b.change90d - a.change90d);

    const result: AltSeasonResult = {
      index: Math.round(index * 100) / 100, // Arrotonda a 2 decimali
      totalAltcoins: top50Altcoins.length,
      outperformingCount,
      btcChange90d,
      cryptos: allProcessedCryptos,
      lastUpdated: new Date().toISOString(),
      status: getMarketStatus(index),
    };

    // Salva in cache
    setCachedData(result);

    return result;
  } catch (error) {
    console.error("Errore nel recupero dati AltSeason:", error);
    throw error;
  }
}

export function getTimeAgo(dateString: string): string {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds} secondi fa`;
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minut${minutes > 1 ? "i" : "o"} fa`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ora${hours > 1 ? "e" : ""} fa`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} giorno${days > 1 ? "i" : ""} fa`;
  }
}

export function getNextUpdateTime(): string {
  const now = new Date();
  const nextUpdate = new Date(now.getTime() + 15 * 60 * 1000); // +15 minuti
  return nextUpdate.toLocaleTimeString("it-IT", {
    hour: "2-digit",
    minute: "2-digit",
  });
}
